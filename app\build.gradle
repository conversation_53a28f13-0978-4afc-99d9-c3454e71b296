plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'com.example.iot_lab4_20212529'
    compileSdk 36

    defaultConfig {
        applicationId "com.example.iot_lab4_20212529"
        minSdk 31
        targetSdk 36
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout

    // Navigation Component
    implementation 'androidx.navigation:navigation-fragment:2.7.5'
    implementation 'androidx.navigation:navigation-ui:2.7.5'

    // RecyclerView
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // Networking
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // Material Design Components
    implementation 'com.google.android.material:material:1.10.0'

    // Fragment
    implementation 'androidx.fragment:fragment:1.6.2'

    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}