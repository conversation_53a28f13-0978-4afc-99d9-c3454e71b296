package com.example.iot_lab4_20212529.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.iot_lab4_20212529.R;
import com.example.iot_lab4_20212529.adapters.LocationAdapter;
import com.example.iot_lab4_20212529.api.WeatherApiClient;
import com.example.iot_lab4_20212529.models.CurrentWeather;
import com.example.iot_lab4_20212529.models.WeatherLocation;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class LocationFragment extends Fragment {

    private RecyclerView recyclerView;
    private LocationAdapter adapter;
    private List<WeatherLocation> locationList;
    private TextInputEditText etCityName;
    private MaterialButton btnSearch;
    private ProgressBar progressBar;

    // Lista predefinida de ubicaciones
    private final List<WeatherLocation> predefinedLocations = Arrays.asList(
            new WeatherLocation("Lima", "Peru", -12.0464, -77.0428),
            new WeatherLocation("Arequipa", "Peru", -16.4090, -71.5375),
            new WeatherLocation("Cusco", "Peru", -13.5319, -71.9675),
            new WeatherLocation("Trujillo", "Peru", -8.1116, -79.0287),
            new WeatherLocation("Chiclayo", "Peru", -6.7714, -79.8371)
    );

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_location, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);
        setupRecyclerView();
        setupClickListeners();
        
        // Mostrar ubicaciones predefinidas inicialmente
        locationList.addAll(predefinedLocations);
        adapter.notifyDataSetChanged();
    }

    private void initViews(View view) {
        recyclerView = view.findViewById(R.id.recyclerViewLocations);
        etCityName = view.findViewById(R.id.etCityName);
        btnSearch = view.findViewById(R.id.btnSearch);
        progressBar = view.findViewById(R.id.progressBar);
        locationList = new ArrayList<>();
    }

    private void setupRecyclerView() {
        adapter = new LocationAdapter(locationList, this::onLocationClick);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerView.setAdapter(adapter);
    }

    private void setupClickListeners() {
        btnSearch.setOnClickListener(v -> searchLocation());
    }

    private void searchLocation() {
        String cityName = etCityName.getText().toString().trim();
        if (cityName.isEmpty()) {
            Toast.makeText(getContext(), "Por favor ingrese un nombre de ciudad", Toast.LENGTH_SHORT).show();
            return;
        }

        showLoading(true);
        
        Call<CurrentWeather> call = WeatherApiClient.getApiService()
                .getCurrentWeather(cityName, WeatherApiClient.API_KEY, "metric");
        
        call.enqueue(new Callback<CurrentWeather>() {
            @Override
            public void onResponse(Call<CurrentWeather> call, Response<CurrentWeather> response) {
                showLoading(false);
                if (response.isSuccessful() && response.body() != null) {
                    CurrentWeather weather = response.body();
                    WeatherLocation location = new WeatherLocation(
                            weather.getName(),
                            weather.getSys().getCountry(),
                            weather.getCoord().getLat(),
                            weather.getCoord().getLon()
                    );
                    
                    // Agregar nueva ubicación a la lista
                    locationList.add(0, location);
                    adapter.notifyItemInserted(0);
                    recyclerView.scrollToPosition(0);
                    
                    etCityName.setText("");
                } else {
                    Toast.makeText(getContext(), "Ciudad no encontrada", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<CurrentWeather> call, Throwable t) {
                showLoading(false);
                Toast.makeText(getContext(), "Error de conexión: " + t.getMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void onLocationClick(WeatherLocation location) {
        Bundle bundle = new Bundle();
        bundle.putString("cityName", location.getName());
        Navigation.findNavController(requireView())
                .navigate(R.id.action_location_to_forecaster, bundle);
    }

    private void showLoading(boolean show) {
        progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        btnSearch.setEnabled(!show);
    }
}
