package com.example.iot_lab4_20212529.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.iot_lab4_20212529.R;
import com.example.iot_lab4_20212529.adapters.ForecastAdapter;
import com.example.iot_lab4_20212529.api.WeatherApiClient;
import com.example.iot_lab4_20212529.models.WeatherData;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ForecasterFragment extends Fragment {

    private RecyclerView recyclerView;
    private ForecastAdapter adapter;
    private List<WeatherData.WeatherItem> forecastList;
    private ProgressBar progressBar;
    private TextView tvEmptyState;
    private String cityName;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_forecaster, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);
        setupRecyclerView();
        
        // Obtener argumentos
        if (getArguments() != null) {
            cityName = getArguments().getString("cityName");
            if (cityName != null) {
                loadForecast();
            } else {
                showEmptyState(true);
            }
        } else {
            showEmptyState(true);
        }
    }

    private void initViews(View view) {
        recyclerView = view.findViewById(R.id.recyclerViewForecasts);
        progressBar = view.findViewById(R.id.progressBar);
        tvEmptyState = view.findViewById(R.id.tvEmptyState);
        forecastList = new ArrayList<>();
    }

    private void setupRecyclerView() {
        adapter = new ForecastAdapter(forecastList);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerView.setAdapter(adapter);
    }

    private void loadForecast() {
        showLoading(true);
        showEmptyState(false);

        Call<WeatherData> call = WeatherApiClient.getApiService()
                .getForecast(cityName, WeatherApiClient.API_KEY, "metric");

        call.enqueue(new Callback<WeatherData>() {
            @Override
            public void onResponse(Call<WeatherData> call, Response<WeatherData> response) {
                showLoading(false);
                if (response.isSuccessful() && response.body() != null) {
                    WeatherData weatherData = response.body();
                    forecastList.clear();
                    
                    // Filtrar para mostrar solo los próximos 5 días (cada 24 horas)
                    List<WeatherData.WeatherItem> filteredList = new ArrayList<>();
                    for (int i = 0; i < weatherData.getWeatherList().size() && filteredList.size() < 5; i += 8) {
                        filteredList.add(weatherData.getWeatherList().get(i));
                    }
                    
                    forecastList.addAll(filteredList);
                    adapter.notifyDataSetChanged();
                    
                    if (forecastList.isEmpty()) {
                        showEmptyState(true);
                    }
                } else {
                    Toast.makeText(getContext(), "Error al cargar pronósticos", Toast.LENGTH_SHORT).show();
                    showEmptyState(true);
                }
            }

            @Override
            public void onFailure(Call<WeatherData> call, Throwable t) {
                showLoading(false);
                Toast.makeText(getContext(), "Error de conexión: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                showEmptyState(true);
            }
        });
    }

    private void showLoading(boolean show) {
        progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        recyclerView.setVisibility(show ? View.GONE : View.VISIBLE);
    }

    private void showEmptyState(boolean show) {
        tvEmptyState.setVisibility(show ? View.VISIBLE : View.GONE);
        recyclerView.setVisibility(show ? View.GONE : View.VISIBLE);
    }

    public void clearForecasts() {
        forecastList.clear();
        adapter.notifyDataSetChanged();
        showEmptyState(true);
    }
}
