package com.example.iot_lab4_20212529.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.iot_lab4_20212529.R;
import com.example.iot_lab4_20212529.models.WeatherData;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ForecastAdapter extends RecyclerView.Adapter<ForecastAdapter.ForecastViewHolder> {

    private List<WeatherData.WeatherItem> forecasts;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());

    public ForecastAdapter(List<WeatherData.WeatherItem> forecasts) {
        this.forecasts = forecasts;
    }

    @NonNull
    @Override
    public ForecastViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_forecast, parent, false);
        return new ForecastViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ForecastViewHolder holder, int position) {
        WeatherData.WeatherItem forecast = forecasts.get(position);
        holder.bind(forecast, dateFormat);
    }

    @Override
    public int getItemCount() {
        return forecasts.size();
    }

    static class ForecastViewHolder extends RecyclerView.ViewHolder {
        private TextView tvDateTime;
        private TextView tvDescription;
        private TextView tvTemperature;
        private TextView tvFeelsLike;
        private TextView tvHumidity;

        public ForecastViewHolder(@NonNull View itemView) {
            super(itemView);
            tvDateTime = itemView.findViewById(R.id.tvDateTime);
            tvDescription = itemView.findViewById(R.id.tvDescription);
            tvTemperature = itemView.findViewById(R.id.tvTemperature);
            tvFeelsLike = itemView.findViewById(R.id.tvFeelsLike);
            tvHumidity = itemView.findViewById(R.id.tvHumidity);
        }

        public void bind(WeatherData.WeatherItem forecast, SimpleDateFormat dateFormat) {
            // Formatear fecha
            Date date = new Date(forecast.getDateTime() * 1000L);
            tvDateTime.setText(dateFormat.format(date));

            // Descripción del clima
            if (forecast.getWeather() != null && !forecast.getWeather().isEmpty()) {
                tvDescription.setText(forecast.getWeather().get(0).getDescription());
            }

            // Temperatura
            int temp = (int) Math.round(forecast.getMain().getTemperature());
            tvTemperature.setText(temp + "°C");

            // Sensación térmica
            int feelsLike = (int) Math.round(forecast.getMain().getFeelsLike());
            tvFeelsLike.setText("Sensación: " + feelsLike + "°C");

            // Humedad
            tvHumidity.setText("Humedad: " + forecast.getMain().getHumidity() + "%");
        }
    }
}
