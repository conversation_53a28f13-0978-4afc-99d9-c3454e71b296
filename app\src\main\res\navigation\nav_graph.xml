<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/locationFragment">

    <fragment
        android:id="@+id/locationFragment"
        android:name="com.example.iot_lab4_20212529.fragments.LocationFragment"
        android:label="Locations"
        tools:layout="@layout/fragment_location">
        <action
            android:id="@+id/action_location_to_forecaster"
            app:destination="@id/forecasterFragment" />
        <action
            android:id="@+id/action_location_to_future"
            app:destination="@id/futureFragment" />
    </fragment>

    <fragment
        android:id="@+id/forecasterFragment"
        android:name="com.example.iot_lab4_20212529.fragments.ForecasterFragment"
        android:label="Forecaster"
        tools:layout="@layout/fragment_forecaster">
        <argument
            android:name="cityName"
            app:argType="string"
            app:nullable="true" />
        <action
            android:id="@+id/action_forecaster_to_location"
            app:destination="@id/locationFragment" />
        <action
            android:id="@+id/action_forecaster_to_future"
            app:destination="@id/futureFragment" />
    </fragment>

    <fragment
        android:id="@+id/futureFragment"
        android:name="com.example.iot_lab4_20212529.fragments.FutureFragment"
        android:label="Future"
        tools:layout="@layout/fragment_future">
        <argument
            android:name="cityName"
            app:argType="string"
            app:nullable="true" />
        <action
            android:id="@+id/action_future_to_location"
            app:destination="@id/locationFragment" />
        <action
            android:id="@+id/action_future_to_forecaster"
            app:destination="@id/forecasterFragment" />
    </fragment>

</navigation>
