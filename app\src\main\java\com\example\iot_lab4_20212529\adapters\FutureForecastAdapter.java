package com.example.iot_lab4_20212529.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.iot_lab4_20212529.R;
import com.example.iot_lab4_20212529.models.WeatherData;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class FutureForecastAdapter extends RecyclerView.Adapter<FutureForecastAdapter.FutureForecastViewHolder> {

    private List<WeatherData.WeatherItem> forecasts;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("EEEE, dd MMM", Locale.getDefault());

    public FutureForecastAdapter(List<WeatherData.WeatherItem> forecasts) {
        this.forecasts = forecasts;
    }

    @NonNull
    @Override
    public FutureForecastViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_future_forecast, parent, false);
        return new FutureForecastViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FutureForecastViewHolder holder, int position) {
        WeatherData.WeatherItem forecast = forecasts.get(position);
        holder.bind(forecast, dateFormat);
    }

    @Override
    public int getItemCount() {
        return forecasts.size();
    }

    static class FutureForecastViewHolder extends RecyclerView.ViewHolder {
        private TextView tvDate;
        private TextView tvDescription;
        private TextView tvMaxTemp;
        private TextView tvMinTemp;
        private TextView tvHumidity;
        private TextView tvWind;

        public FutureForecastViewHolder(@NonNull View itemView) {
            super(itemView);
            tvDate = itemView.findViewById(R.id.tvDate);
            tvDescription = itemView.findViewById(R.id.tvDescription);
            tvMaxTemp = itemView.findViewById(R.id.tvMaxTemp);
            tvMinTemp = itemView.findViewById(R.id.tvMinTemp);
            tvHumidity = itemView.findViewById(R.id.tvHumidity);
            tvWind = itemView.findViewById(R.id.tvWind);
        }

        public void bind(WeatherData.WeatherItem forecast, SimpleDateFormat dateFormat) {
            // Formatear fecha
            Date date = new Date(forecast.getDateTime() * 1000L);
            tvDate.setText(dateFormat.format(date));

            // Descripción del clima
            if (forecast.getWeather() != null && !forecast.getWeather().isEmpty()) {
                tvDescription.setText(forecast.getWeather().get(0).getDescription());
            }

            // Temperaturas (usando temp como máxima y temp_min como mínima)
            int maxTemp = (int) Math.round(forecast.getMain().getTempMax());
            int minTemp = (int) Math.round(forecast.getMain().getTempMin());
            tvMaxTemp.setText(maxTemp + "°");
            tvMinTemp.setText(minTemp + "°");

            // Humedad
            tvHumidity.setText("Humedad: " + forecast.getMain().getHumidity() + "%");

            // Viento
            if (forecast.getWind() != null) {
                double windSpeed = forecast.getWind().getSpeed() * 3.6; // Convertir m/s a km/h
                tvWind.setText("Viento: " + Math.round(windSpeed) + " km/h");
            } else {
                tvWind.setText("Viento: N/A");
            }
        }
    }
}
