package com.example.iot_lab4_20212529.api;

import com.example.iot_lab4_20212529.models.CurrentWeather;
import com.example.iot_lab4_20212529.models.WeatherData;

import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

public interface WeatherApiService {
    
    // Método GET 1: Obtener ubicaciones por nombre
    @GET("weather")
    Call<CurrentWeather> getCurrentWeather(
            @Query("q") String cityName,
            @Query("appid") String apiKey,
            @Query("units") String units
    );
    
    // Método GET 2: Obtener pronósticos para 5 días
    @GET("forecast")
    Call<WeatherData> getForecast(
            @Query("q") String cityName,
            @Query("appid") String apiKey,
            @Query("units") String units
    );
    
    // Método GET 3: Obtener pronósticos futuros por coordenadas
    @GET("forecast")
    Call<WeatherData> getForecastByCoordinates(
            @Query("lat") double latitude,
            @Query("lon") double longitude,
            @Query("appid") String apiKey,
            @Query("units") String units
    );
    
    // Método GET 4: Obtener historial (simulado con forecast)
    @GET("forecast")
    Call<WeatherData> getHistoricalData(
            @Query("q") String cityName,
            @Query("appid") String apiKey,
            @Query("units") String units,
            @Query("cnt") int count
    );
}
