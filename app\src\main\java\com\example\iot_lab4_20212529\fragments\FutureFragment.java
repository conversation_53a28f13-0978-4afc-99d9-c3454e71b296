package com.example.iot_lab4_20212529.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.iot_lab4_20212529.R;
import com.example.iot_lab4_20212529.adapters.FutureForecastAdapter;
import com.example.iot_lab4_20212529.api.WeatherApiClient;
import com.example.iot_lab4_20212529.models.WeatherData;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class FutureFragment extends Fragment {

    private RecyclerView recyclerView;
    private FutureForecastAdapter adapter;
    private List<WeatherData.WeatherItem> futureList;
    private ProgressBar progressBar;
    private TextView tvEmptyState;
    private String cityName;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_future, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);
        setupRecyclerView();
        
        // Obtener argumentos
        if (getArguments() != null) {
            cityName = getArguments().getString("cityName");
            if (cityName != null) {
                loadFutureForecast();
            } else {
                showEmptyState(true);
            }
        } else {
            showEmptyState(true);
        }
    }

    private void initViews(View view) {
        recyclerView = view.findViewById(R.id.recyclerViewFuture);
        progressBar = view.findViewById(R.id.progressBar);
        tvEmptyState = view.findViewById(R.id.tvEmptyState);
        futureList = new ArrayList<>();
    }

    private void setupRecyclerView() {
        adapter = new FutureForecastAdapter(futureList);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerView.setAdapter(adapter);
    }

    private void loadFutureForecast() {
        showLoading(true);
        showEmptyState(false);

        Call<WeatherData> call = WeatherApiClient.getApiService()
                .getForecast(cityName, WeatherApiClient.API_KEY, "metric");

        call.enqueue(new Callback<WeatherData>() {
            @Override
            public void onResponse(Call<WeatherData> call, Response<WeatherData> response) {
                showLoading(false);
                if (response.isSuccessful() && response.body() != null) {
                    WeatherData weatherData = response.body();
                    futureList.clear();
                    
                    // Obtener pronósticos para los próximos días (cada 24 horas a partir del día siguiente)
                    List<WeatherData.WeatherItem> allForecasts = weatherData.getWeatherList();
                    if (allForecasts.size() > 8) { // Saltar el primer día
                        for (int i = 8; i < allForecasts.size() && futureList.size() < 4; i += 8) {
                            futureList.add(allForecasts.get(i));
                        }
                    }
                    
                    adapter.notifyDataSetChanged();
                    
                    if (futureList.isEmpty()) {
                        showEmptyState(true);
                    }
                } else {
                    Toast.makeText(getContext(), "Error al cargar pronósticos futuros", Toast.LENGTH_SHORT).show();
                    showEmptyState(true);
                }
            }

            @Override
            public void onFailure(Call<WeatherData> call, Throwable t) {
                showLoading(false);
                Toast.makeText(getContext(), "Error de conexión: " + t.getMessage(), Toast.LENGTH_SHORT).show();
                showEmptyState(true);
            }
        });
    }

    private void showLoading(boolean show) {
        progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        recyclerView.setVisibility(show ? View.GONE : View.VISIBLE);
    }

    private void showEmptyState(boolean show) {
        tvEmptyState.setVisibility(show ? View.VISIBLE : View.GONE);
        recyclerView.setVisibility(show ? View.GONE : View.VISIBLE);
    }

    public void clearForecasts() {
        futureList.clear();
        adapter.notifyDataSetChanged();
        showEmptyState(true);
    }
}
