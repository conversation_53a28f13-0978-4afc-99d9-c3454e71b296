package com.example.iot_lab4_20212529.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.iot_lab4_20212529.R;
import com.example.iot_lab4_20212529.models.WeatherLocation;

import java.util.List;

public class LocationAdapter extends RecyclerView.Adapter<LocationAdapter.LocationViewHolder> {

    private List<WeatherLocation> locations;
    private OnLocationClickListener listener;

    public interface OnLocationClickListener {
        void onLocationClick(WeatherLocation location);
    }

    public LocationAdapter(List<WeatherLocation> locations, OnLocationClickListener listener) {
        this.locations = locations;
        this.listener = listener;
    }

    @NonNull
    @Override
    public LocationViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_location, parent, false);
        return new LocationViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull LocationViewHolder holder, int position) {
        WeatherLocation location = locations.get(position);
        holder.bind(location, listener);
    }

    @Override
    public int getItemCount() {
        return locations.size();
    }

    static class LocationViewHolder extends RecyclerView.ViewHolder {
        private TextView tvLocationName;
        private TextView tvLocationCoords;

        public LocationViewHolder(@NonNull View itemView) {
            super(itemView);
            tvLocationName = itemView.findViewById(R.id.tvLocationName);
            tvLocationCoords = itemView.findViewById(R.id.tvLocationCoords);
        }

        public void bind(WeatherLocation location, OnLocationClickListener listener) {
            tvLocationName.setText(location.toString());
            tvLocationCoords.setText(String.format("Lat: %.4f, Lon: %.4f", 
                    location.getLat(), location.getLon()));

            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onLocationClick(location);
                }
            });
        }
    }
}
