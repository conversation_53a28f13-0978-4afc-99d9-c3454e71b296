package com.example.iot_lab4_20212529.models;

import com.google.gson.annotations.SerializedName;
import java.util.List;

public class CurrentWeather {
    @SerializedName("coord")
    private Coord coord;
    
    @SerializedName("weather")
    private List<WeatherData.Weather> weather;
    
    @SerializedName("base")
    private String base;
    
    @SerializedName("main")
    private WeatherData.Main main;
    
    @SerializedName("visibility")
    private int visibility;
    
    @SerializedName("wind")
    private WeatherData.Wind wind;
    
    @SerializedName("clouds")
    private Clouds clouds;
    
    @SerializedName("dt")
    private long dt;
    
    @SerializedName("sys")
    private Sys sys;
    
    @SerializedName("timezone")
    private int timezone;
    
    @SerializedName("id")
    private int id;
    
    @SerializedName("name")
    private String name;
    
    @SerializedName("cod")
    private int cod;

    // Getters and Setters
    public Coord getCoord() {
        return coord;
    }

    public void setCoord(Coord coord) {
        this.coord = coord;
    }

    public List<WeatherData.Weather> getWeather() {
        return weather;
    }

    public void setWeather(List<WeatherData.Weather> weather) {
        this.weather = weather;
    }

    public String getBase() {
        return base;
    }

    public void setBase(String base) {
        this.base = base;
    }

    public WeatherData.Main getMain() {
        return main;
    }

    public void setMain(WeatherData.Main main) {
        this.main = main;
    }

    public int getVisibility() {
        return visibility;
    }

    public void setVisibility(int visibility) {
        this.visibility = visibility;
    }

    public WeatherData.Wind getWind() {
        return wind;
    }

    public void setWind(WeatherData.Wind wind) {
        this.wind = wind;
    }

    public Clouds getClouds() {
        return clouds;
    }

    public void setClouds(Clouds clouds) {
        this.clouds = clouds;
    }

    public long getDt() {
        return dt;
    }

    public void setDt(long dt) {
        this.dt = dt;
    }

    public Sys getSys() {
        return sys;
    }

    public void setSys(Sys sys) {
        this.sys = sys;
    }

    public int getTimezone() {
        return timezone;
    }

    public void setTimezone(int timezone) {
        this.timezone = timezone;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCod() {
        return cod;
    }

    public void setCod(int cod) {
        this.cod = cod;
    }

    public static class Coord {
        @SerializedName("lon")
        private double lon;
        
        @SerializedName("lat")
        private double lat;

        public double getLon() {
            return lon;
        }

        public void setLon(double lon) {
            this.lon = lon;
        }

        public double getLat() {
            return lat;
        }

        public void setLat(double lat) {
            this.lat = lat;
        }
    }

    public static class Clouds {
        @SerializedName("all")
        private int all;

        public int getAll() {
            return all;
        }

        public void setAll(int all) {
            this.all = all;
        }
    }

    public static class Sys {
        @SerializedName("type")
        private int type;
        
        @SerializedName("id")
        private int id;
        
        @SerializedName("country")
        private String country;
        
        @SerializedName("sunrise")
        private long sunrise;
        
        @SerializedName("sunset")
        private long sunset;

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public long getSunrise() {
            return sunrise;
        }

        public void setSunrise(long sunrise) {
            this.sunrise = sunrise;
        }

        public long getSunset() {
            return sunset;
        }

        public void setSunset(long sunset) {
            this.sunset = sunset;
        }
    }
}
